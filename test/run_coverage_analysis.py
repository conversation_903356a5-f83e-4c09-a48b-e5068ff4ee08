#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Tuple

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from pxl.util.logging_config import get_logger


class CoverageAnalyzer:
    """Analyzes test coverage for the refactored calculator components."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.project_root = Path(__file__).parent.parent
        self.test_dir = self.project_root / "test" / "unit_test"
        self.src_dir = self.project_root / "src"
        
        # Target coverage threshold
        self.target_coverage = 90.0
        
        # Refactored modules to analyze
        self.refactored_modules = [
            "pxl.calc.base",
            "pxl.calc.data_manager",
            "pxl.calc.computation",
            "pxl.calc.state_manager",
            "pxl.calc.figure_manager",
            "pxl.calc.de_calculator",
            "pxl.calc.gdmb_calculator",
            "pxl.calc.compatibility",
        ]
        
        # Test files for refactored components
        self.test_files = [
            "test_refactored_calculators.py",
            "test_data_manager.py",
            "test_computation_engines.py",
            "test_state_manager.py",
            "test_figure_manager.py",
            "test_performance_concurrency.py",
            "test_integration_compatibility.py",
            "test_de_calc.py",  # Updated with refactored tests
            "test_gdmb_calc.py",  # Updated with refactored tests
        ]
    
    def install_coverage_tool(self) -> bool:
        """Install coverage tool if not available."""
        try:
            import coverage
            return True
        except ImportError:
            self.logger.info("Installing coverage tool...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", "coverage"], 
                             check=True, capture_output=True)
                return True
            except subprocess.CalledProcessError as e:
                self.logger.error(f"Failed to install coverage tool: {e}")
                return False
    
    def run_tests_with_coverage(self) -> bool:
        """Run tests with coverage measurement."""
        if not self.install_coverage_tool():
            return False
        
        self.logger.info("Running tests with coverage measurement...")
        
        # Change to project root directory
        os.chdir(self.project_root)
        
        try:
            # Initialize coverage
            cmd = [sys.executable, "-m", "coverage", "erase"]
            subprocess.run(cmd, check=True, capture_output=True)
            
            # Run each test file with coverage
            for test_file in self.test_files:
                test_path = self.test_dir / test_file
                if test_path.exists():
                    self.logger.info(f"Running {test_file}...")
                    cmd = [
                        sys.executable, "-m", "coverage", "run", 
                        "--append", "--source=src/pxl/calc", 
                        str(test_path)
                    ]
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode != 0:
                        self.logger.warning(f"Test {test_file} had issues: {result.stderr}")
                else:
                    self.logger.warning(f"Test file {test_file} not found")
            
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Failed to run tests with coverage: {e}")
            return False
    
    def generate_coverage_report(self) -> Dict:
        """Generate coverage report."""
        self.logger.info("Generating coverage report...")
        
        try:
            # Generate text report
            cmd = [sys.executable, "-m", "coverage", "report", "--format=text"]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            text_report = result.stdout
            
            # Generate JSON report for detailed analysis
            cmd = [sys.executable, "-m", "coverage", "json"]
            subprocess.run(cmd, check=True, capture_output=True)
            
            # Load JSON report
            coverage_json_path = self.project_root / "coverage.json"
            if coverage_json_path.exists():
                with open(coverage_json_path, 'r') as f:
                    json_report = json.load(f)
            else:
                json_report = {}
            
            # Generate HTML report
            html_dir = self.project_root / "htmlcov"
            cmd = [sys.executable, "-m", "coverage", "html", "-d", str(html_dir)]
            subprocess.run(cmd, check=True, capture_output=True)
            
            return {
                'text_report': text_report,
                'json_report': json_report,
                'html_dir': str(html_dir)
            }
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Failed to generate coverage report: {e}")
            return {}
    
    def analyze_coverage_results(self, coverage_data: Dict) -> Dict:
        """Analyze coverage results and provide recommendations."""
        analysis = {
            'overall_coverage': 0.0,
            'module_coverage': {},
            'low_coverage_files': [],
            'missing_lines': {},
            'recommendations': []
        }
        
        if not coverage_data.get('json_report'):
            return analysis
        
        json_report = coverage_data['json_report']
        
        # Overall coverage
        totals = json_report.get('totals', {})
        if totals.get('num_statements', 0) > 0:
            analysis['overall_coverage'] = (
                totals.get('covered_lines', 0) / totals.get('num_statements', 1) * 100
            )
        
        # Module-specific coverage
        files = json_report.get('files', {})
        for file_path, file_data in files.items():
            # Convert file path to module name
            if 'src/pxl/calc' in file_path:
                module_name = file_path.replace('src/', '').replace('/', '.').replace('.py', '')
                
                if file_data.get('summary', {}).get('num_statements', 0) > 0:
                    coverage_percent = (
                        file_data['summary']['covered_lines'] / 
                        file_data['summary']['num_statements'] * 100
                    )
                    analysis['module_coverage'][module_name] = coverage_percent
                    
                    # Identify low coverage files
                    if coverage_percent < self.target_coverage:
                        analysis['low_coverage_files'].append({
                            'module': module_name,
                            'coverage': coverage_percent,
                            'missing_lines': file_data.get('missing_lines', [])
                        })
                        analysis['missing_lines'][module_name] = file_data.get('missing_lines', [])
        
        # Generate recommendations
        analysis['recommendations'] = self._generate_recommendations(analysis)
        
        return analysis
    
    def _generate_recommendations(self, analysis: Dict) -> List[str]:
        """Generate recommendations based on coverage analysis."""
        recommendations = []
        
        overall_coverage = analysis['overall_coverage']
        
        if overall_coverage < self.target_coverage:
            recommendations.append(
                f"Overall coverage ({overall_coverage:.1f}%) is below target ({self.target_coverage}%). "
                "Consider adding more comprehensive tests."
            )
        
        # Recommendations for low coverage modules
        for low_cov_file in analysis['low_coverage_files']:
            module = low_cov_file['module']
            coverage = low_cov_file['coverage']
            missing_lines = low_cov_file['missing_lines']
            
            recommendations.append(
                f"Module {module} has low coverage ({coverage:.1f}%). "
                f"Missing coverage for lines: {missing_lines[:10]}{'...' if len(missing_lines) > 10 else ''}"
            )
        
        # Specific recommendations for refactored components
        module_coverage = analysis['module_coverage']
        
        if module_coverage.get('pxl.calc.data_manager', 0) < self.target_coverage:
            recommendations.append(
                "Add more tests for DataManager edge cases: buffer overflow, concurrent access, "
                "data validation with various input types."
            )
        
        if module_coverage.get('pxl.calc.computation', 0) < self.target_coverage:
            recommendations.append(
                "Add more tests for computation engines: error handling, boundary conditions, "
                "numerical precision, and performance edge cases."
            )
        
        if module_coverage.get('pxl.calc.state_manager', 0) < self.target_coverage:
            recommendations.append(
                "Add more tests for StateManager: timer edge cases, concurrent state changes, "
                "cleanup scenarios, and error recovery."
            )
        
        if module_coverage.get('pxl.calc.figure_manager', 0) < self.target_coverage:
            recommendations.append(
                "Add more tests for FigureManager: different backends, error conditions, "
                "memory management, and dynamic update scenarios."
            )
        
        return recommendations
    
    def print_coverage_summary(self, analysis: Dict, coverage_data: Dict):
        """Print coverage summary to console."""
        print("\n" + "="*80)
        print("REFACTORED CALCULATOR COMPONENTS - COVERAGE ANALYSIS REPORT")
        print("="*80)
        
        print(f"\nOverall Coverage: {analysis['overall_coverage']:.1f}%")
        print(f"Target Coverage: {self.target_coverage}%")
        
        status = "✅ PASSED" if analysis['overall_coverage'] >= self.target_coverage else "❌ FAILED"
        print(f"Status: {status}")
        
        print("\nModule Coverage:")
        print("-" * 50)
        for module, coverage in sorted(analysis['module_coverage'].items()):
            status_icon = "✅" if coverage >= self.target_coverage else "❌"
            print(f"{status_icon} {module:<40} {coverage:>6.1f}%")
        
        if analysis['low_coverage_files']:
            print(f"\nLow Coverage Files ({len(analysis['low_coverage_files'])}):")
            print("-" * 50)
            for file_info in analysis['low_coverage_files']:
                print(f"❌ {file_info['module']:<40} {file_info['coverage']:>6.1f}%")
        
        if analysis['recommendations']:
            print(f"\nRecommendations ({len(analysis['recommendations'])}):")
            print("-" * 50)
            for i, rec in enumerate(analysis['recommendations'], 1):
                print(f"{i}. {rec}")
        
        if coverage_data.get('html_dir'):
            print(f"\nDetailed HTML report available at: {coverage_data['html_dir']}/index.html")
        
        print("\n" + "="*80)
    
    def run_analysis(self) -> bool:
        """Run complete coverage analysis."""
        self.logger.info("Starting coverage analysis for refactored calculator components...")
        
        # Run tests with coverage
        if not self.run_tests_with_coverage():
            self.logger.error("Failed to run tests with coverage")
            return False
        
        # Generate coverage report
        coverage_data = self.generate_coverage_report()
        if not coverage_data:
            self.logger.error("Failed to generate coverage report")
            return False
        
        # Analyze results
        analysis = self.analyze_coverage_results(coverage_data)
        
        # Print summary
        self.print_coverage_summary(analysis, coverage_data)
        
        # Return success if overall coverage meets target
        return analysis['overall_coverage'] >= self.target_coverage


def main():
    """Main function to run coverage analysis."""
    analyzer = CoverageAnalyzer()
    success = analyzer.run_analysis()
    
    if success:
        print("\n✅ Coverage analysis completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Coverage analysis failed or coverage below target!")
        sys.exit(1)


if __name__ == '__main__':
    main()
