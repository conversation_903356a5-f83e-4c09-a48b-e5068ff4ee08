#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import os
import sys
import unittest
import time
import argparse
from pathlib import Path
from typing import List, Dict, Tuple

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from pxl.util.logging_config import get_logger


class RefactoredTestRunner:
    """Comprehensive test runner for refactored calculator components."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.project_root = Path(__file__).parent.parent
        self.test_dir = self.project_root / "test" / "unit_test"
        
        # Test suites organized by category
        self.test_suites = {
            'component_tests': [
                'test_data_manager.py',
                'test_computation_engines.py',
                'test_state_manager.py',
                'test_figure_manager.py',
            ],
            'refactored_tests': [
                'test_refactored_calculators.py',
            ],
            'compatibility_tests': [
                'test_de_calc.py',  # Updated with refactored comparison tests
                'test_gdmb_calc.py',  # Updated with refactored comparison tests
            ],
            'integration_tests': [
                'test_integration_compatibility.py',
            ],
            'performance_tests': [
                'test_performance_concurrency.py',
            ],
        }
        
        # All test files
        self.all_test_files = []
        for suite_tests in self.test_suites.values():
            self.all_test_files.extend(suite_tests)
    
    def discover_tests(self, test_files: List[str] = None) -> unittest.TestSuite:
        """Discover and load tests from specified files."""
        if test_files is None:
            test_files = self.all_test_files
        
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        # Change to test directory
        original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        try:
            for test_file in test_files:
                test_path = self.test_dir / test_file
                if test_path.exists():
                    self.logger.info(f"Loading tests from {test_file}")
                    
                    # Import the test module
                    module_name = test_file.replace('.py', '')
                    try:
                        import importlib.util
                        spec = importlib.util.spec_from_file_location(module_name, test_path)
                        module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(module)

                        # Load tests from module
                        module_suite = loader.loadTestsFromModule(module)
                        suite.addTest(module_suite)
                        
                    except Exception as e:
                        self.logger.error(f"Failed to load tests from {test_file}: {e}")
                else:
                    self.logger.warning(f"Test file {test_file} not found")
        
        finally:
            os.chdir(original_cwd)
        
        return suite
    
    def run_test_suite(self, suite: unittest.TestSuite, verbosity: int = 2) -> unittest.TestResult:
        """Run a test suite and return results."""
        runner = unittest.TextTestRunner(
            verbosity=verbosity,
            stream=sys.stdout,
            buffer=True,
            failfast=False
        )
        
        self.logger.info(f"Running {suite.countTestCases()} tests...")
        start_time = time.time()
        
        result = runner.run(suite)
        
        end_time = time.time()
        duration = end_time - start_time
        
        self.logger.info(f"Tests completed in {duration:.2f} seconds")
        
        return result
    
    def run_suite_by_category(self, category: str, verbosity: int = 2) -> unittest.TestResult:
        """Run tests for a specific category."""
        if category not in self.test_suites:
            raise ValueError(f"Unknown test category: {category}")
        
        self.logger.info(f"Running {category} tests...")
        test_files = self.test_suites[category]
        suite = self.discover_tests(test_files)
        
        return self.run_test_suite(suite, verbosity)
    
    def run_all_tests(self, verbosity: int = 2) -> Dict[str, unittest.TestResult]:
        """Run all test categories and return results."""
        results = {}
        
        self.logger.info("Running all refactored calculator tests...")
        
        for category in self.test_suites.keys():
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"RUNNING {category.upper()}")
            self.logger.info(f"{'='*60}")
            
            try:
                result = self.run_suite_by_category(category, verbosity)
                results[category] = result
            except Exception as e:
                self.logger.error(f"Failed to run {category}: {e}")
                # Create a dummy failed result
                result = unittest.TestResult()
                result.errors.append((None, str(e)))
                results[category] = result
        
        return results
    
    def print_summary(self, results: Dict[str, unittest.TestResult]):
        """Print test results summary."""
        print("\n" + "="*80)
        print("REFACTORED CALCULATOR TESTS - SUMMARY REPORT")
        print("="*80)
        
        total_tests = 0
        total_failures = 0
        total_errors = 0
        total_skipped = 0
        
        print(f"\n{'Category':<25} {'Tests':<8} {'Pass':<8} {'Fail':<8} {'Error':<8} {'Skip':<8} {'Status':<10}")
        print("-" * 80)
        
        for category, result in results.items():
            tests_run = result.testsRun
            failures = len(result.failures)
            errors = len(result.errors)
            skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
            passed = tests_run - failures - errors - skipped
            
            status = "✅ PASS" if failures == 0 and errors == 0 else "❌ FAIL"
            
            print(f"{category:<25} {tests_run:<8} {passed:<8} {failures:<8} {errors:<8} {skipped:<8} {status:<10}")
            
            total_tests += tests_run
            total_failures += failures
            total_errors += errors
            total_skipped += skipped
        
        print("-" * 80)
        total_passed = total_tests - total_failures - total_errors - total_skipped
        overall_status = "✅ PASS" if total_failures == 0 and total_errors == 0 else "❌ FAIL"
        
        print(f"{'TOTAL':<25} {total_tests:<8} {total_passed:<8} {total_failures:<8} {total_errors:<8} {total_skipped:<8} {overall_status:<10}")
        
        # Print detailed failure information
        if total_failures > 0 or total_errors > 0:
            print(f"\nFAILURES AND ERRORS:")
            print("-" * 50)
            
            for category, result in results.items():
                if result.failures:
                    print(f"\n{category} - FAILURES:")
                    for test, traceback in result.failures:
                        print(f"  ❌ {test}")
                        print(f"     {traceback.split('AssertionError:')[-1].strip()}")
                
                if result.errors:
                    print(f"\n{category} - ERRORS:")
                    for test, traceback in result.errors:
                        print(f"  💥 {test}")
                        print(f"     {traceback.split('Exception:')[-1].strip()}")
        
        print("\n" + "="*80)
        
        return total_failures == 0 and total_errors == 0
    
    def run_quick_smoke_test(self) -> bool:
        """Run a quick smoke test to verify basic functionality."""
        self.logger.info("Running quick smoke test...")
        
        # Test basic imports
        try:
            from pxl.calc.de_calculator import DeltaECalculatorRefactored, DeltaECalculatorConfig
            from pxl.calc.gdmb_calculator import GDMBCalculatorRefactored, GDMBCalculatorConfig
            from pxl.calc.compatibility import DeltaECalculatorAdapter
            from pxl.calc.data_manager import DataManager
            from pxl.calc.computation import DeltaEComputation, GDMBComputation
            from pxl.calc.state_manager import StateManager
            from pxl.calc.figure_manager import FigureManager
            
            self.logger.info("✅ All imports successful")
        except Exception as e:
            self.logger.error(f"❌ Import failed: {e}")
            return False
        
        # Test basic instantiation
        try:
            # Test DataManager
            dm = DataManager()
            self.logger.info("✅ DataManager instantiation successful")
            
            # Test StateManager
            sm = StateManager()
            self.logger.info("✅ StateManager instantiation successful")
            
            # Test computation engines
            de_comp = DeltaEComputation()
            gdmb_comp = GDMBComputation(tmax=500.0)
            self.logger.info("✅ Computation engines instantiation successful")
            
            # Test FigureManager
            fm = FigureManager(show_figure=False, dynamic=False)
            fm.cleanup()
            self.logger.info("✅ FigureManager instantiation successful")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Instantiation failed: {e}")
            return False


def main():
    """Main function to run refactored calculator tests."""
    parser = argparse.ArgumentParser(description="Run refactored calculator tests")
    parser.add_argument('--category', choices=['component_tests', 'refactored_tests', 
                                              'compatibility_tests', 'integration_tests', 
                                              'performance_tests'], 
                       help="Run tests for specific category only")
    parser.add_argument('--smoke-test', action='store_true', 
                       help="Run quick smoke test only")
    parser.add_argument('--verbose', '-v', action='count', default=1,
                       help="Increase verbosity (use -vv for more verbose)")
    
    args = parser.parse_args()
    
    runner = RefactoredTestRunner()
    
    if args.smoke_test:
        success = runner.run_quick_smoke_test()
        if success:
            print("\n✅ Smoke test passed!")
            sys.exit(0)
        else:
            print("\n❌ Smoke test failed!")
            sys.exit(1)
    
    if args.category:
        # Run specific category
        try:
            result = runner.run_suite_by_category(args.category, args.verbose)
            success = len(result.failures) == 0 and len(result.errors) == 0
            
            if success:
                print(f"\n✅ {args.category} tests passed!")
                sys.exit(0)
            else:
                print(f"\n❌ {args.category} tests failed!")
                sys.exit(1)
        except Exception as e:
            print(f"\n❌ Failed to run {args.category}: {e}")
            sys.exit(1)
    else:
        # Run all tests
        results = runner.run_all_tests(args.verbose)
        success = runner.print_summary(results)
        
        if success:
            print("\n✅ All tests passed!")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)


if __name__ == '__main__':
    main()
