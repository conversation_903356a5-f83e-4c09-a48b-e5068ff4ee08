#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import unittest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from io import BytesIO

import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend for testing
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec

from pxl.calc.figure_manager import FigureManager
from pxl.util.pusher import BytesIOPusher
from pxl.util.logging_config import get_logger


class TestFigureManager(unittest.TestCase):
    """Test cases for FigureManager class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
        # Use non-interactive backend and don't show figures during testing
        self.figure_manager = FigureManager(
            figure_size=(10, 6),
            show_figure=False,
            dynamic=False
        )
    
    def tearDown(self):
        """Clean up after tests."""
        self.figure_manager.cleanup()
        plt.close('all')
    
    def test_figure_manager_initialization(self):
        """Test figure manager initialization."""
        self.assertEqual(self.figure_manager._figure_size, (10, 6))
        self.assertFalse(self.figure_manager._show_figure)
        self.assertFalse(self.figure_manager._dynamic)
        self.assertIsNone(self.figure_manager._bytesio_pusher)
        self.assertIsNone(self.figure_manager._logo)
        self.assertIsNone(self.figure_manager.figure)
        self.assertEqual(len(self.figure_manager.axes), 0)
    
    def test_figure_creation(self):
        """Test figure creation."""
        # Create figure
        fig = self.figure_manager.create_figure()
        
        # Verify figure was created
        self.assertIsNotNone(fig)
        self.assertEqual(self.figure_manager.figure, fig)
        self.assertEqual(fig.get_size_inches().tolist(), [10, 6])
    
    def test_axis_operations(self):
        """Test axis operations."""
        # Create figure first
        self.figure_manager.create_figure()
        
        # Add axes
        ax1 = self.figure_manager.add_axis('plot1', 2, 1, 1)
        ax2 = self.figure_manager.add_axis('plot2', 2, 1, 2)
        
        # Verify axes were added
        self.assertIsNotNone(ax1)
        self.assertIsNotNone(ax2)
        self.assertEqual(len(self.figure_manager.axes), 2)
        self.assertIn('plot1', self.figure_manager.axes)
        self.assertIn('plot2', self.figure_manager.axes)
        
        # Test getting specific axis
        retrieved_ax1 = self.figure_manager.get_axis('plot1')
        self.assertEqual(ax1, retrieved_ax1)
        
        # Test getting non-existent axis
        non_existent_ax = self.figure_manager.get_axis('non_existent')
        self.assertIsNone(non_existent_ax)
    
    def test_axis_without_figure_error(self):
        """Test that adding axis without figure raises error."""
        with self.assertRaises(RuntimeError):
            self.figure_manager.add_axis('plot1', 1, 1, 1)
    
    def test_gridspec_layout(self):
        """Test gridspec layout creation."""
        # Create figure first
        self.figure_manager.create_figure()
        
        # Create gridspec
        gs = self.figure_manager.create_gridspec_layout(2, 3, hspace=0.3, wspace=0.2)
        
        # Verify gridspec was created
        self.assertIsInstance(gs, gridspec.GridSpec)
        self.assertEqual(gs.get_geometry(), (2, 3))
    
    def test_gridspec_without_figure_error(self):
        """Test that creating gridspec without figure raises error."""
        with self.assertRaises(RuntimeError):
            self.figure_manager.create_gridspec_layout(2, 2)
    
    def test_figure_saving(self):
        """Test figure saving functionality."""
        # Create figure and add some content
        self.figure_manager.create_figure()
        ax = self.figure_manager.add_axis('test_plot', 1, 1, 1)
        ax.plot([1, 2, 3], [1, 4, 2])
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            tmp_filename = tmp_file.name
        
        try:
            self.figure_manager.save_figure(tmp_filename, dpi=150)
            
            # Verify file was created
            self.assertTrue(os.path.exists(tmp_filename))
            self.assertGreater(os.path.getsize(tmp_filename), 0)
        finally:
            # Clean up
            if os.path.exists(tmp_filename):
                os.unlink(tmp_filename)
    
    def test_save_without_figure_error(self):
        """Test that saving without figure raises error."""
        with tempfile.NamedTemporaryFile(suffix='.png') as tmp_file:
            with self.assertRaises(RuntimeError):
                self.figure_manager.save_figure(tmp_file.name)
    
    @patch('pxl.util.watermark.add_watermark2')
    def test_watermark_addition(self, mock_add_watermark):
        """Test watermark addition."""
        # Create figure manager with logo
        logo_data = "fake_logo_data"
        fm_with_logo = FigureManager(
            show_figure=False,
            dynamic=False,
            logo=logo_data
        )
        
        try:
            # Create figure
            fm_with_logo.create_figure()
            
            # Add watermark
            fm_with_logo.add_watermark(size=(0.2, 0.2), alpha=0.05)
            
            # Verify watermark function was called
            mock_add_watermark.assert_called_once_with(
                fm_with_logo.figure, logo_data, size=(0.2, 0.2), alpha=0.05
            )
        finally:
            fm_with_logo.cleanup()
    
    def test_watermark_without_logo(self):
        """Test watermark addition without logo."""
        # Create figure
        self.figure_manager.create_figure()
        
        # Add watermark (should do nothing since no logo)
        self.figure_manager.add_watermark()
        # Should not raise any errors
    
    def test_bytesio_pusher_integration(self):
        """Test BytesIO pusher integration."""
        # Create mock pusher
        mock_pusher = Mock(spec=BytesIOPusher)
        
        # Create figure manager with pusher
        fm_with_pusher = FigureManager(
            show_figure=False,
            dynamic=False,
            bytesio_pusher=mock_pusher
        )
        
        try:
            # Create figure and add content
            fm_with_pusher.create_figure()
            ax = fm_with_pusher.add_axis('test_plot', 1, 1, 1)
            ax.plot([1, 2, 3], [1, 4, 2])
            
            # Push figure data
            fm_with_pusher.push_figure_data()
            
            # Verify pusher was called
            mock_pusher.push.assert_called_once()
            
            # Verify the argument is a BytesIO object
            call_args = mock_pusher.push.call_args[0]
            self.assertEqual(len(call_args), 1)
            self.assertIsInstance(call_args[0], BytesIO)
        finally:
            fm_with_pusher.cleanup()
    
    def test_push_without_pusher(self):
        """Test pushing figure data without pusher."""
        # Create figure
        self.figure_manager.create_figure()
        
        # Push figure data (should do nothing since no pusher)
        self.figure_manager.push_figure_data()
        # Should not raise any errors
    
    def test_redraw_functionality(self):
        """Test figure redraw functionality."""
        # Create figure
        self.figure_manager.create_figure()
        
        # Mock the canvas methods
        with patch.object(self.figure_manager.figure.canvas, 'draw_idle') as mock_draw, \
             patch.object(self.figure_manager.figure.canvas, 'flush_events') as mock_flush:
            
            # Redraw
            self.figure_manager.redraw()
            
            # Verify canvas methods were called
            mock_draw.assert_called_once()
            mock_flush.assert_called_once()
    
    def test_redraw_without_figure(self):
        """Test redraw without figure."""
        # Redraw without figure (should do nothing)
        self.figure_manager.redraw()
        # Should not raise any errors
    
    def test_tight_layout(self):
        """Test tight layout functionality."""
        # Create figure
        self.figure_manager.create_figure()
        
        # Mock plt.tight_layout
        with patch('matplotlib.pyplot.tight_layout') as mock_tight_layout:
            self.figure_manager.tight_layout()
            mock_tight_layout.assert_called_once()
    
    def test_dynamic_updates(self):
        """Test dynamic updates functionality."""
        # Create figure manager with dynamic mode
        fm_dynamic = FigureManager(
            show_figure=False,
            dynamic=True
        )
        
        try:
            # Create figure
            fm_dynamic.create_figure()
            
            # Mock timer
            mock_timer = Mock()
            with patch.object(fm_dynamic.figure.canvas, 'new_timer', return_value=mock_timer):
                # Start dynamic updates
                callback = Mock()
                fm_dynamic.start_dynamic_updates(1.0, callback)
                
                # Verify timer was configured
                mock_timer.add_callback.assert_called_once_with(callback)
                mock_timer.start.assert_called_once()
                
                # Stop dynamic updates
                fm_dynamic.stop_dynamic_updates()
                mock_timer.stop.assert_called_once()
        finally:
            fm_dynamic.cleanup()
    
    def test_dynamic_updates_without_dynamic_mode(self):
        """Test dynamic updates without dynamic mode."""
        # Create figure
        self.figure_manager.create_figure()
        
        # Start dynamic updates (should do nothing since not dynamic)
        callback = Mock()
        self.figure_manager.start_dynamic_updates(1.0, callback)
        # Should not raise any errors
    
    def test_figure_close(self):
        """Test figure closing."""
        # Create figure and axes
        self.figure_manager.create_figure()
        self.figure_manager.add_axis('plot1', 1, 1, 1)
        
        # Verify figure and axes exist
        self.assertIsNotNone(self.figure_manager.figure)
        self.assertEqual(len(self.figure_manager.axes), 1)
        
        # Close figure
        self.figure_manager.close()
        
        # Verify figure and axes are cleared
        self.assertIsNone(self.figure_manager.figure)
        self.assertEqual(len(self.figure_manager.axes), 0)
    
    def test_cleanup(self):
        """Test cleanup functionality."""
        # Create figure with dynamic updates
        fm_with_timer = FigureManager(
            show_figure=False,
            dynamic=True
        )
        
        # Create figure and start timer
        fm_with_timer.create_figure()
        mock_timer = Mock()
        fm_with_timer._timer = mock_timer
        
        # Cleanup
        fm_with_timer.cleanup()
        
        # Verify timer was stopped and figure was closed
        mock_timer.stop.assert_called_once()
        self.assertIsNone(fm_with_timer.figure)


if __name__ == '__main__':
    unittest.main()
