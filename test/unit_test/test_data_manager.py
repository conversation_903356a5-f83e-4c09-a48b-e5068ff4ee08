#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import unittest
import numpy as np
import queue
import threading
import time
from unittest.mock import Mock, patch

from pxl.calc.data_manager import DataManager, DataBuffer
from pxl.util.exceptions import DataProcessingError
from pxl.util.logging_config import get_logger


class TestDataBuffer(unittest.TestCase):
    """Test cases for DataBuffer class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
    
    def test_buffer_initialization(self):
        """Test buffer initialization."""
        # Test unlimited buffer
        buffer = DataBuffer()
        self.assertTrue(buffer.empty())
        self.assertEqual(buffer.qsize(), 0)
        
        # Test limited buffer
        buffer_limited = DataBuffer(maxsize=5)
        self.assertTrue(buffer_limited.empty())
        self.assertEqual(buffer_limited.qsize(), 0)
    
    def test_buffer_put_get(self):
        """Test buffer put and get operations."""
        buffer = DataBuffer()
        
        # Test put and get
        test_data = "test_data"
        buffer.put(test_data)
        self.assertFalse(buffer.empty())
        self.assertEqual(buffer.qsize(), 1)
        
        retrieved_data = buffer.get()
        self.assertEqual(retrieved_data, test_data)
        self.assertTrue(buffer.empty())
    
    def test_buffer_full_behavior(self):
        """Test buffer behavior when full."""
        buffer = DataBuffer(maxsize=2)
        
        # Fill buffer
        buffer.put("data1")
        buffer.put("data2")
        self.assertEqual(buffer.qsize(), 2)
        
        # Test non-blocking put when full
        with self.assertRaises(queue.Full):
            buffer.put("data3", block=False)
    
    def test_buffer_timeout(self):
        """Test buffer timeout behavior."""
        buffer = DataBuffer(maxsize=1)
        buffer.put("data1")
        
        # Test timeout on put
        start_time = time.time()
        with self.assertRaises(queue.Full):
            buffer.put("data2", block=True, timeout=0.1)
        elapsed_time = time.time() - start_time
        self.assertGreaterEqual(elapsed_time, 0.1)
    
    def test_buffer_thread_safety(self):
        """Test buffer thread safety."""
        buffer = DataBuffer()
        results = []
        
        def producer():
            for i in range(10):
                buffer.put(f"data_{i}")
        
        def consumer():
            for _ in range(10):
                data = buffer.get()
                results.append(data)
        
        # Start threads
        producer_thread = threading.Thread(target=producer)
        consumer_thread = threading.Thread(target=consumer)
        
        producer_thread.start()
        consumer_thread.start()
        
        producer_thread.join()
        consumer_thread.join()
        
        # Verify all data was processed
        self.assertEqual(len(results), 10)
        self.assertTrue(all(item.startswith("data_") for item in results))


class TestDataManager(unittest.TestCase):
    """Test cases for DataManager class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
        self.data_manager = DataManager()
    
    def test_data_manager_initialization(self):
        """Test data manager initialization."""
        self.assertIsNotNone(self.data_manager.buffer)
        self.assertTrue(self.data_manager.buffer.empty())
        self.assertIsNone(self.data_manager.reference_white)
        self.assertEqual(self.data_manager.scale_multiplier, 1.0)
    
    def test_reference_white_operations(self):
        """Test reference white operations."""
        ref_white = np.array([0.3, 0.3, 0.3])
        
        # Set reference white
        self.data_manager.set_reference_white(ref_white)
        np.testing.assert_array_equal(self.data_manager.reference_white, ref_white)
        
        # Test with different reference white
        new_ref_white = np.array([0.31, 0.32, 0.33])
        self.data_manager.set_reference_white(new_ref_white)
        np.testing.assert_array_equal(self.data_manager.reference_white, new_ref_white)
    
    def test_scale_multiplier_operations(self):
        """Test scale multiplier operations."""
        # Test default value
        self.assertEqual(self.data_manager.scale_multiplier, 1.0)
        
        # Test setting scale multiplier
        self.data_manager.set_scale_multiplier(2.5)
        self.assertEqual(self.data_manager.scale_multiplier, 2.5)

        # Test with small positive value
        self.data_manager.set_scale_multiplier(0.1)
        self.assertEqual(self.data_manager.scale_multiplier, 0.1)

        # Test with zero (should raise error based on implementation)
        with self.assertRaises(Exception):
            self.data_manager.set_scale_multiplier(0.0)

        # Test with negative value (should raise error based on implementation)
        with self.assertRaises(Exception):
            self.data_manager.set_scale_multiplier(-1.0)
    
    def test_xyz_data_validation(self):
        """Test XYZ data validation."""
        # Valid XYZ data
        valid_xyz = np.array([0.3, 0.3, 0.3])
        self.data_manager.validate_xyz_data(valid_xyz)  # Should not raise
        
        # Invalid XYZ data - wrong shape
        with self.assertRaises(DataProcessingError):
            self.data_manager.validate_xyz_data(np.array([0.3, 0.3]))
        
        # Invalid XYZ data - wrong dimensions
        with self.assertRaises(DataProcessingError):
            self.data_manager.validate_xyz_data(np.array([[0.3, 0.3, 0.3], [0.4, 0.4, 0.4]]))
        
        # Invalid XYZ data - contains NaN
        with self.assertRaises(DataProcessingError):
            self.data_manager.validate_xyz_data(np.array([0.3, np.nan, 0.3]))
        
        # Invalid XYZ data - contains Inf
        with self.assertRaises(DataProcessingError):
            self.data_manager.validate_xyz_data(np.array([0.3, np.inf, 0.3]))
        
        # Invalid XYZ data - negative values
        with self.assertRaises(DataProcessingError):
            self.data_manager.validate_xyz_data(np.array([-0.1, 0.3, 0.3]))
    
    def test_y_data_validation(self):
        """Test Y data validation."""
        # Valid Y data
        self.data_manager.validate_y_data(100.0)  # Should not raise
        self.data_manager.validate_y_data(0.0)    # Zero should be valid
        
        # Invalid Y data - negative
        with self.assertRaises(DataProcessingError):
            self.data_manager.validate_y_data(-1.0)
        
        # Invalid Y data - NaN
        with self.assertRaises(DataProcessingError):
            self.data_manager.validate_y_data(np.nan)
        
        # Invalid Y data - Inf
        with self.assertRaises(DataProcessingError):
            self.data_manager.validate_y_data(np.inf)
        
        # Invalid Y data - not a number
        with self.assertRaises(DataProcessingError):
            self.data_manager.validate_y_data("not_a_number")
    
    def test_data_history_operations(self):
        """Test data history operations."""
        # Test initial state
        self.assertEqual(len(self.data_manager.get_data_history()), 0)
        
        # Add data points
        self.data_manager.add_data_point(0, "data1")
        self.data_manager.add_data_point(1, "data2")
        
        history = self.data_manager.get_data_history()
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0], (0, "data1"))
        self.assertEqual(history[1], (1, "data2"))
        
        # Test clear history
        self.data_manager.clear_history()
        self.assertEqual(len(self.data_manager.get_data_history()), 0)
    
    def test_data_history_maxlen(self):
        """Test data history maximum length."""
        # Add more than maxlen data points
        for i in range(1500):  # maxlen is 1000
            self.data_manager.add_data_point(i, f"data_{i}")
        
        history = self.data_manager.get_data_history()
        self.assertEqual(len(history), 1000)  # Should be limited to maxlen
        
        # Verify it contains the most recent data
        self.assertEqual(history[-1], (1499, "data_1499"))
        self.assertEqual(history[0], (500, "data_500"))  # First 500 should be dropped
    
    def test_statistics(self):
        """Test statistics gathering."""
        # Initial statistics
        stats = self.data_manager.get_statistics()
        self.assertEqual(stats['buffer_size'], 0)
        self.assertEqual(stats['history_size'], 0)
        self.assertFalse(stats['has_reference_white'])
        self.assertEqual(stats['scale_multiplier'], 1.0)
        
        # Add some data and reference white
        self.data_manager.buffer.put("test_data")
        self.data_manager.add_data_point(0, "data")
        self.data_manager.set_reference_white(np.array([0.3, 0.3, 0.3]))
        self.data_manager.set_scale_multiplier(2.0)
        
        # Updated statistics
        stats = self.data_manager.get_statistics()
        self.assertEqual(stats['buffer_size'], 1)
        self.assertEqual(stats['history_size'], 1)
        self.assertTrue(stats['has_reference_white'])
        self.assertEqual(stats['scale_multiplier'], 2.0)


if __name__ == '__main__':
    unittest.main()
