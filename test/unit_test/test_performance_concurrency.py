#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import unittest
import time
import threading
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
import os
from unittest.mock import Mock

from pxl.calc.de_calculator import DeltaECalculatorRefactored, DeltaECalculatorConfig, ScaleTarget
from pxl.calc.gdmb_calculator import GDMBCalculatorRefactored, GDMBCalculatorConfig
from pxl.calc.compatibility import DeltaECalculatorAdapter
from pxl.calc.data_manager import DataManager, DataBuffer
from pxl.calc.computation import DeltaEComputation, GDMBComputation
from pxl.calc.state_manager import StateManager
from pxl.de_calc.de_calc import DeltaECalculator
from pxl.gdmb_calc.gdmb_calc import GDMBCalculator
from pxl.util.logging_config import get_logger


class TestPerformanceComparison(unittest.TestCase):
    """Test cases for performance comparison between original and refactored calculators."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
        self.test_data_size = 1000
        
        # Generate test data
        np.random.seed(42)  # For reproducible results
        self.target_xyz_data = np.random.uniform(0.1, 0.9, (self.test_data_size, 3))
        self.measured_xyz_data = self.target_xyz_data + np.random.normal(0, 0.01, (self.test_data_size, 3))
        
        self.target_y_data = np.random.uniform(10, 500, self.test_data_size)
        self.measured_y_data = self.target_y_data + np.random.normal(0, 5, self.test_data_size)
    
    def test_delta_e_performance_comparison(self):
        """Compare performance between original and refactored Delta E calculators."""
        # Test original calculator
        start_time = time.time()
        original_calc = DeltaECalculator(
            dynamic=False,
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=DeltaECalculator.EnumScaleTarget.AUTO,
            show_figure=False
        )
        
        original_calc.set_reference_white(self.target_xyz_data[0], self.measured_xyz_data[0])
        for i in range(min(100, self.test_data_size)):  # Limit to 100 for performance
            original_calc.receive_data(i, self.target_xyz_data[i], self.measured_xyz_data[i])
        
        original_time = time.time() - start_time
        original_calc.cleanup()
        
        # Test refactored calculator
        start_time = time.time()
        refactored_calc = DeltaECalculatorAdapter(
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=DeltaECalculatorAdapter.EnumScaleTarget.AUTO,
            dynamic=False,
            show_figure=False
        )
        
        refactored_calc.set_reference_white(self.target_xyz_data[0], self.measured_xyz_data[0])
        for i in range(min(100, self.test_data_size)):
            refactored_calc.receive_data(i, self.target_xyz_data[i], self.measured_xyz_data[i])
        
        refactored_time = time.time() - start_time
        refactored_calc.cleanup()
        
        # Log performance results
        self.logger.info(f"Original Delta E Calculator time: {original_time:.4f}s")
        self.logger.info(f"Refactored Delta E Calculator time: {refactored_time:.4f}s")
        self.logger.info(f"Performance ratio (refactored/original): {refactored_time/original_time:.2f}")
        
        # Refactored version should not be significantly slower (allow 50% overhead for separation of concerns)
        self.assertLess(refactored_time, original_time * 1.5, 
                       "Refactored calculator should not be significantly slower")
    
    def test_gdmb_performance_comparison(self):
        """Compare performance between original and refactored GDMB calculators."""
        tmax = np.max(self.target_y_data)
        
        # Test original calculator
        start_time = time.time()
        original_calc = GDMBCalculator(
            tmax=tmax,
            debug=False,
            dynamic=False,
            show_figure=False
        )
        
        for i in range(min(100, self.test_data_size)):
            original_calc.receive_data(i, self.target_y_data[i], self.measured_y_data[i])
        
        original_time = time.time() - start_time
        original_calc.cleanup()
        
        # Test refactored calculator
        start_time = time.time()
        config = GDMBCalculatorConfig(
            tmax=tmax,
            debug=False,
            dynamic=False,
            show_figure=False
        )
        refactored_calc = GDMBCalculatorRefactored(config)
        refactored_calc.initialize()
        
        for i in range(min(100, self.test_data_size)):
            refactored_calc.receive_data(i, self.target_y_data[i], self.measured_y_data[i])
        
        refactored_time = time.time() - start_time
        refactored_calc.cleanup()
        
        # Log performance results
        self.logger.info(f"Original GDMB Calculator time: {original_time:.4f}s")
        self.logger.info(f"Refactored GDMB Calculator time: {refactored_time:.4f}s")
        self.logger.info(f"Performance ratio (refactored/original): {refactored_time/original_time:.2f}")
        
        # Refactored version should not be significantly slower
        self.assertLess(refactored_time, original_time * 1.5,
                       "Refactored calculator should not be significantly slower")
    
    def test_memory_usage_comparison(self):
        """Compare memory usage between original and refactored calculators."""
        process = psutil.Process(os.getpid())
        
        # Measure baseline memory
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Test original calculator memory usage
        original_calc = DeltaECalculator(
            dynamic=False,
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=DeltaECalculator.EnumScaleTarget.AUTO,
            show_figure=False
        )
        
        original_calc.set_reference_white(self.target_xyz_data[0], self.measured_xyz_data[0])
        for i in range(min(500, self.test_data_size)):
            original_calc.receive_data(i, self.target_xyz_data[i], self.measured_xyz_data[i])
        
        original_memory = process.memory_info().rss / 1024 / 1024  # MB
        original_calc.cleanup()
        
        # Test refactored calculator memory usage
        refactored_calc = DeltaECalculatorAdapter(
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=DeltaECalculatorAdapter.EnumScaleTarget.AUTO,
            dynamic=False,
            show_figure=False
        )
        
        refactored_calc.set_reference_white(self.target_xyz_data[0], self.measured_xyz_data[0])
        for i in range(min(500, self.test_data_size)):
            refactored_calc.receive_data(i, self.target_xyz_data[i], self.measured_xyz_data[i])
        
        refactored_memory = process.memory_info().rss / 1024 / 1024  # MB
        refactored_calc.cleanup()
        
        # Log memory usage results
        original_usage = original_memory - baseline_memory
        refactored_usage = refactored_memory - baseline_memory
        
        self.logger.info(f"Original calculator memory usage: {original_usage:.2f} MB")
        self.logger.info(f"Refactored calculator memory usage: {refactored_usage:.2f} MB")
        
        # Memory usage should be reasonable (allow some overhead for better structure)
        if original_usage > 0:
            self.assertLess(refactored_usage, original_usage * 2.0,
                           "Refactored calculator should not use excessive memory")


class TestConcurrencyAndThreadSafety(unittest.TestCase):
    """Test cases for concurrency and thread safety."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
    
    def test_data_buffer_thread_safety(self):
        """Test DataBuffer thread safety under concurrent access."""
        buffer = DataBuffer(maxsize=100)
        results = []
        errors = []
        
        def producer(producer_id, count):
            try:
                for i in range(count):
                    data = f"producer_{producer_id}_item_{i}"
                    buffer.put(data, timeout=1.0)
            except Exception as e:
                errors.append(f"Producer {producer_id}: {e}")
        
        def consumer(consumer_id, count):
            try:
                for i in range(count):
                    data = buffer.get(timeout=1.0)
                    results.append((consumer_id, data))
            except Exception as e:
                errors.append(f"Consumer {consumer_id}: {e}")
        
        # Start multiple producers and consumers
        threads = []
        items_per_producer = 20
        num_producers = 3
        num_consumers = 2
        
        # Start producers
        for i in range(num_producers):
            thread = threading.Thread(target=producer, args=(i, items_per_producer))
            threads.append(thread)
            thread.start()
        
        # Start consumers
        total_items = num_producers * items_per_producer
        items_per_consumer = total_items // num_consumers
        for i in range(num_consumers):
            count = items_per_consumer + (1 if i < total_items % num_consumers else 0)
            thread = threading.Thread(target=consumer, args=(i, count))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join(timeout=5.0)
        
        # Verify results
        self.assertEqual(len(errors), 0, f"Errors occurred: {errors}")
        self.assertEqual(len(results), total_items, "All items should be consumed")
    
    def test_state_manager_thread_safety(self):
        """Test StateManager thread safety under concurrent access."""
        state_manager = StateManager()
        results = []
        errors = []
        
        def worker(worker_id, operations):
            try:
                for i in range(operations):
                    # Set and get flags
                    flag_name = f"flag_{worker_id}_{i}"
                    state_manager.set_flag(flag_name, True)
                    flag_value = state_manager.get_flag(flag_name)
                    
                    # Increment counters
                    counter_name = f"counter_{worker_id}"
                    state_manager.increment_counter(counter_name, 1)
                    counter_value = state_manager.get_counter(counter_name)
                    
                    results.append((worker_id, i, flag_value, counter_value))
            except Exception as e:
                errors.append(f"Worker {worker_id}: {e}")
        
        # Start multiple workers
        threads = []
        num_workers = 5
        operations_per_worker = 50
        
        for i in range(num_workers):
            thread = threading.Thread(target=worker, args=(i, operations_per_worker))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join(timeout=5.0)
        
        # Verify results
        self.assertEqual(len(errors), 0, f"Errors occurred: {errors}")
        self.assertEqual(len(results), num_workers * operations_per_worker)
        
        # Verify final counter values
        for worker_id in range(num_workers):
            counter_value = state_manager.get_counter(f"counter_{worker_id}")
            self.assertEqual(counter_value, operations_per_worker)
    
    def test_concurrent_calculator_usage(self):
        """Test concurrent usage of refactored calculators."""
        results = []
        errors = []
        
        def calculator_worker(worker_id):
            try:
                # Create refactored calculator
                config = DeltaECalculatorConfig(
                    max_de_threshold=(2.5, 10.0, float('inf')),
                    scale_target=ScaleTarget.AUTO,
                    dynamic=False,
                    show_figure=False
                )
                calc = DeltaECalculatorRefactored(config)
                calc.initialize()
                
                # Process some data
                target_xyz = np.array([0.3, 0.3, 0.3])
                measured_xyz = np.array([0.31, 0.29, 0.32])
                
                calc.set_reference_white(target_xyz, measured_xyz)
                
                for i in range(10):
                    calc.receive_data(i, target_xyz, measured_xyz)
                
                report = calc.get_report()
                results.append((worker_id, report.max_de, report.avg_de))
                
                calc.cleanup()
                
            except Exception as e:
                errors.append(f"Worker {worker_id}: {e}")
        
        # Start multiple calculator workers
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(calculator_worker, i) for i in range(8)]
            
            # Wait for completion
            for future in as_completed(futures, timeout=30):
                future.result()  # This will raise any exceptions
        
        # Verify results
        self.assertEqual(len(errors), 0, f"Errors occurred: {errors}")
        self.assertEqual(len(results), 8)
        
        # All calculators should produce similar results
        max_des = [result[1] for result in results]
        avg_des = [result[2] for result in results]
        
        # Results should be consistent (all non-zero and similar)
        for max_de in max_des:
            self.assertGreater(max_de, 0)
        for avg_de in avg_des:
            self.assertGreater(avg_de, 0)
        
        # Standard deviation should be small (results should be consistent)
        max_de_std = np.std(max_des)
        avg_de_std = np.std(avg_des)
        self.assertLess(max_de_std, 0.1, "Max Delta E results should be consistent")
        self.assertLess(avg_de_std, 0.1, "Average Delta E results should be consistent")


if __name__ == '__main__':
    unittest.main()
