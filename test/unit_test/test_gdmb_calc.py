#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: November 13, 2024
"""

import os
import unittest

import numpy as np
import pandas as pd
from parameterized import parameterized

from pxl.gdmb_calc.gdmb_calc import GDMBCalculator
from pxl.calc.gdmb_calculator import GDMBCalculatorRefactored, GDMBCalculatorConfig
from pxl.util.error_handling import log_and_raise
from pxl.util.exceptions import CalculationError
from pxl.util.logging_config import get_logger
from pxl.dlb.watermarks import WATERMARK_DLB_BASE64
from pxl.util.gdmb_sheet import GDMBCalculatorExcelSheet


class TestGDMBComparison(unittest.TestCase):
    @parameterized.expand([
        (os.path.abspath(os.path.join(
            os.path.dirname(__file__),
            "../test_sheets/"
            "GDMB_Calculator_v1.1.xlsm")
        )),
    ])
    def test_gdmb_comparison(self, gdmb_sheet):
        try:
            sheet = GDMBCalculatorExcelSheet(gdmb_sheet)
            measurement_y_np = sheet.get_measurement_y()
            target_y_np = sheet.get_target_y()
            gdmb_ref = sheet.get_scaled_data()
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"Failed to load Excel sheet: {str(e)}", exc_info=True)
            raise
        else:
            try:
                gdmb_calc: GDMBCalculator = GDMBCalculator(tmax=np.max(target_y_np),
                                                           debug=True,
                                                           dynamic=False,
                                                           show_figure=False,
                                                           logo=WATERMARK_DLB_BASE64)
            except Exception as e:
                logger = get_logger(__name__)
                logger.error(f"Failed to create GDMBCalculator: {str(e)}", exc_info=True)
                raise
            else:
                gdmb_calc.batch_receive_data(target_y_list=target_y_np,
                                             measurement_y_list=measurement_y_np)
                # plot and save figure upon completion
                gdmb_calc.plot_figure(save_figure=None)
                gdmb_dut = gdmb_calc.scaled_data
                print(f"gdmb_dut: {gdmb_dut}\n gdmb_tgt: {gdmb_ref}")

                # Select the shorter list
                min_length = min(len(gdmb_dut), len(gdmb_ref))
                A_short = gdmb_dut[:min_length]
                B_short = gdmb_ref[:min_length]

                # Comparison
                comparison = np.abs(np.array(A_short) - np.array(B_short)) < 1e-2
                self.assertTrue(np.all(comparison), f"Arrays differ: {A_short} vs {B_short}")


class TestGDMBCalculator(unittest.TestCase):
    def test_dynamic_mode_positive(self):
        import threading  # Threading import call
        # Read csv file and move contents to numpy arrays
        target_file: str = os.path.abspath(os.path.join("test\\unit_test\\gdmb_csv\\positive_data", 'target_data.csv'))
        measurement_file: str = os.path.abspath(
            os.path.join("test\\unit_test\\gdmb_csv\\positive_data", 'measurement_data.csv'))
        target_df = pd.read_csv(target_file, names=['Section', "Nits"])
        target_np = target_df.Nits.to_numpy()
        data_frame = pd.read_csv(measurement_file)
        measurement_np = data_frame.Y.to_numpy()

        # Thread function call
        def external_data_thread(calculator, interval):
            for i in range(measurement_np.size):
                target_y = target_np[i * 4]  # Target Step = 20
                measurement_y = measurement_np[i]  # Measurement Step = 80
                calculator.receive_data(i, target_y, measurement_y, redraw=True)
            calculator.done()
        try:
            gdmb_calc: GDMBCalculator = GDMBCalculator(tmax=np.max(target_np), debug=False, dynamic=True,
                                                       show_figure=False,
                                                       logo=WATERMARK_DLB_BASE64)
            # run function call here
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"Failed to create GDMBCalculator: {str(e)}", exc_info=True)
            raise
        else:
            try:
                thread = threading.Thread(target=external_data_thread, args=(gdmb_calc, 2.5,))
                thread.daemon = True
                thread.start()
                gdmb_calc.plot_figure()
                # Check that scaling is applied
                assert gdmb_calc.scale_target == "Y"
                # Check that scaled data is within target bounds
                lower_bound_bools = np.less(gdmb_calc.lower_tar, gdmb_calc.scaled_data)
                upper_bound_bools = np.greater(gdmb_calc.upper_tar, gdmb_calc.scaled_data)
                lb_set = set(lower_bound_bools)
                ub_set = set(upper_bound_bools)
                assert False not in lb_set and False not in ub_set
            except Exception:
                print("Test Dynamic Mode Positive Failed.")
                raise Exception("Test Dynamic Mode Positive Failed.")

    def test_negative_same_measurement(self):
        """
        Negative Test Case in which luminance data values remain constant. Validates that GDMB Test
        terminates measurement in response.
        """
        import threading  # Threading import call
        # Read csv file and move contents to numpy arrays
        target_file: str = os.path.abspath(
            os.path.join("test\\unit_test\\gdmb_csv\\negative_data_same", 'target_data.csv'))
        measurement_file: str = os.path.abspath(
            os.path.join("test\\unit_test\\gdmb_csv\\negative_data_same", 'measurement_data.csv'))
        target_df = pd.read_csv(target_file, names=['Section', "Nits"])
        target_np = target_df.Nits.to_numpy()
        data_frame = pd.read_csv(measurement_file)
        measurement_np = data_frame.Y.to_numpy()

        # Thread function call
        def external_data_thread(calculator, interval):
            for i in range(measurement_np.size):
                target_y = target_np[i * 4]  # Target Step = 20
                measurement_y = measurement_np[i]  # Measurement Step = 80
                calculator.receive_data(i, target_y, measurement_y, redraw=True)
            calculator.done()

        try:
            gdmb_calc: GDMBCalculator = GDMBCalculator(tmax=np.max(target_np), debug=False, dynamic=True,
                                                       show_figure=False,
                                                       logo=WATERMARK_DLB_BASE64)
            # run function call here
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"Failed to create GDMBCalculator: {str(e)}", exc_info=True)
            raise
        else:
            try:
                thread = threading.Thread(target=external_data_thread, args=(gdmb_calc, 2.5,))
                thread.daemon = True
                thread.start()
                gdmb_calc.plot_figure()
                # Check that scaling is applied
                assert gdmb_calc.scale_target == "N"
            except Exception:
                print("Test Negative Same Measurement Failed.")
                raise Exception("Test Negative Same Measurement Failed.")

    def test_dynamic_mode_negative(self):
        import threading  # Threading import call
        # Read csv file and move contents to numpy arrays
        target_file: str = os.path.abspath(os.path.join("test\\unit_test\\gdmb_csv\\negative_data", 'target_data.csv'))
        measurement_file: str = os.path.abspath(
            os.path.join("test\\unit_test\\gdmb_csv\\negative_data", 'measurement_data.csv'))
        target_df = pd.read_csv(target_file, names=['Section', "Nits"])
        target_np = target_df.Nits.to_numpy()
        data_frame = pd.read_csv(measurement_file)
        measurement_np = data_frame.Y.to_numpy()

        # Thread function call
        def external_data_thread(calculator, interval):
            for i in range(measurement_np.size):
                target_y = target_np[i * 4]  # Target Step = 20
                measurement_y = measurement_np[i]  # Measurement Step = 80
                calculator.receive_data(i, target_y, measurement_y, redraw=True)
            calculator.done()
        try:
            gdmb_calc: GDMBCalculator = GDMBCalculator(tmax=np.max(target_np), debug=False, dynamic=True,
                                                       show_figure=False,
                                                       logo=WATERMARK_DLB_BASE64)
            # run function call here
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"Failed to create GDMBCalculator: {str(e)}", exc_info=True)
            raise
        else:
            try:
                thread = threading.Thread(target=external_data_thread, args=(gdmb_calc, 2.5,))
                thread.daemon = True
                thread.start()
                gdmb_calc.plot_figure()
                # Check that scaling is applied
                assert gdmb_calc.scale_target == "N"
            except Exception:
                print("Test Dynamic Mode Negative Failed.")
                raise Exception("Test Dynamic Mode Negative Failed.")

    def test_static_mode_positive(self):
        # Read csv file and move contents to numpy arrays
        target_file: str = os.path.abspath(os.path.join("test\\unit_test\\gdmb_csv\\positive_data", 'target_data.csv'))
        measurement_file: str = os.path.abspath(
            os.path.join("test\\unit_test\\gdmb_csv\\positive_data", 'measurement_data.csv'))
        target_df = pd.read_csv(target_file, names=['Section', "Nits"])
        target_np = target_df.Nits.to_numpy()
        data_frame = pd.read_csv(measurement_file)
        measurement_np = data_frame.Y.to_numpy()

        # Thread function call
        try:
            gdmb_calc: GDMBCalculator = GDMBCalculator(tmax=np.max(target_np), debug=True, dynamic=False,
                                                       show_figure=False,
                                                       logo=WATERMARK_DLB_BASE64)
            # run function call here
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"Failed to create GDMBCalculator: {str(e)}", exc_info=True)
            raise
        else:
            try:
                gdmb_calc.batch_receive_data(target_y_list=target_np, measurement_y_list=measurement_np)
                gdmb_calc.plot_figure()
                # Check that scaling is applied
                assert gdmb_calc.scale_target == "Y"
                # Check that scaled data is within target bounds
                lower_bound_bools = np.less(gdmb_calc.lower_tar, gdmb_calc.scaled_data)
                upper_bound_bools = np.greater(gdmb_calc.upper_tar, gdmb_calc.scaled_data)
                lb_set = set(lower_bound_bools)
                ub_set = set(upper_bound_bools)
                assert False not in lb_set and False not in ub_set
            except Exception:
                print("Test Static Mode Positive Failed.")
                raise Exception("Test Static Mode Positive Failed.")

    def test_static_mode_negative(self):
        # Read csv file and move contents to numpy arrays
        target_file: str = os.path.abspath(os.path.join("test\\unit_test\\gdmb_csv\\negative_data", 'target_data.csv'))
        measurement_file: str = os.path.abspath(
            os.path.join("test\\unit_test\\gdmb_csv\\negative_data", 'measurement_data.csv'))
        target_df = pd.read_csv(target_file, names=['Section', "Nits"])
        target_np = target_df.Nits.to_numpy()
        data_frame = pd.read_csv(measurement_file)
        measurement_np = data_frame.Y.to_numpy()

        # Thread function call
        try:
            gdmb_calc: GDMBCalculator = GDMBCalculator(tmax=np.max(target_np), debug=False, dynamic=False,
                                                       show_figure=False,
                                                       logo=WATERMARK_DLB_BASE64)
            # run function call here
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"Failed to create GDMBCalculator: {str(e)}", exc_info=True)
            raise
        else:
            try:
                gdmb_calc.batch_receive_data(target_y_list=target_np, measurement_y_list=measurement_np)
                gdmb_calc.plot_figure()
                assert gdmb_calc.scale_target == "N"
            except Exception:
                print("Test Static Mode Negative Failed.")
                raise Exception("Test Static Mode Negative Failed.")


class TestGDMBCalculatorRefactoredComparison(unittest.TestCase):
    """Test cases comparing original and refactored GDMB calculators."""

    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)

        # Common test data
        self.target_y = 100.0
        self.measured_y = 95.0
        self.tmax = 500.0

    def test_refactored_vs_original_basic_calculation(self):
        """Test that refactored calculator produces same results as original."""
        # Create original calculator
        original_calc = GDMBCalculator(
            tmax=self.tmax,
            debug=False,
            dynamic=False,
            show_figure=False,
            logo=WATERMARK_DLB_BASE64
        )

        # Create refactored calculator
        config = GDMBCalculatorConfig(
            tmax=self.tmax,
            debug=False,
            dynamic=False,
            show_figure=False
        )
        refactored_calc = GDMBCalculatorRefactored(config)
        refactored_calc.initialize()

        # Process same data
        original_max_flag = original_calc.receive_data(0, self.target_y, self.measured_y)
        refactored_max_flag = refactored_calc.receive_data(0, self.target_y, self.measured_y)

        # Compare max flags
        self.assertEqual(original_max_flag, refactored_max_flag,
                        "Max flags should match between original and refactored")

        # Compare scale factors
        original_scale = original_calc.scale_factor()
        refactored_scale = refactored_calc.scale_factor()

        self.assertAlmostEqual(original_scale, refactored_scale, places=4,
                              msg="Scale factors should match")

        # Compare reports
        original_report = original_calc.get_report()
        refactored_report = refactored_calc.get_report()

        self.assertAlmostEqual(original_report['tmax'], refactored_report['tmax'], places=4)
        self.assertAlmostEqual(original_report['mmax'], refactored_report['mmax'], places=4)

        # Cleanup
        original_calc.cleanup()
        refactored_calc.cleanup()

    def test_refactored_batch_processing(self):
        """Test refactored calculator with batch data processing."""
        # Test data
        target_y_list = np.array([100.0, 200.0, 300.0, 400.0, 500.0])
        measurement_y_list = np.array([95.0, 190.0, 285.0, 380.0, 475.0])

        # Create refactored calculator
        config = GDMBCalculatorConfig(
            tmax=np.max(target_y_list),
            debug=False,
            dynamic=False,
            show_figure=False
        )
        refactored_calc = GDMBCalculatorRefactored(config)
        refactored_calc.initialize()

        # Process batch data
        refactored_calc.batch_receive_data(target_y_list, measurement_y_list)

        # Verify data was processed
        report = refactored_calc.get_report()
        self.assertEqual(report['tmax'], np.max(target_y_list))
        self.assertGreater(report['mmax'], 0)

        # Verify scale factor calculation
        scale_factor = refactored_calc.scale_factor()
        self.assertIsInstance(scale_factor, float)
        self.assertGreater(scale_factor, 0)

        refactored_calc.cleanup()

    @parameterized.expand([
        (os.path.abspath(os.path.join(
            os.path.dirname(__file__),
            "../test_sheets/"
            "GDMB_Calculator_v1.1.xlsm")
        )),
    ])
    def test_refactored_excel_sheet_compatibility(self, gdmb_sheet):
        """Test refactored calculator with Excel sheet data."""
        try:
            sheet = GDMBCalculatorExcelSheet(gdmb_sheet)
            measurement_y_np = sheet.get_measurement_y()
            target_y_np = sheet.get_target_y()
            gdmb_ref = sheet.get_scaled_data()
        except Exception as e:
            self.logger.error(f"Failed to load Excel sheet: {str(e)}", exc_info=True)
            raise

        # Test with refactored calculator
        config = GDMBCalculatorConfig(
            tmax=np.max(target_y_np),
            debug=False,
            dynamic=False,
            show_figure=False
        )
        refactored_calc = GDMBCalculatorRefactored(config)
        refactored_calc.initialize()

        # Process data
        refactored_calc.batch_receive_data(target_y_np, measurement_y_np)
        refactored_calc.plot_figure(save_figure=None)

        # Get scaled data from refactored calculator
        scaled_data_refactored = refactored_calc._computation.scaled_data

        # Compare with reference (allowing for small differences)
        if len(scaled_data_refactored) > 0 and len(gdmb_ref) > 0:
            min_length = min(len(scaled_data_refactored), len(gdmb_ref))
            scaled_short = scaled_data_refactored[:min_length]
            ref_short = gdmb_ref[:min_length]

            # Allow for small numerical differences
            comparison = np.abs(np.array(scaled_short) - np.array(ref_short)) < 1e-1
            self.assertTrue(np.all(comparison),
                           f"Refactored scaled data differs from reference")

        # Test report
        report = refactored_calc.get_report()
        self.assertGreater(report['tmax'], 0.0)
        self.assertGreaterEqual(report['mmax'], 0.0)

        refactored_calc.cleanup()

    def test_refactored_component_separation(self):
        """Test that refactored calculator properly separates concerns."""
        config = GDMBCalculatorConfig(
            tmax=self.tmax,
            debug=False,
            dynamic=False,
            show_figure=False
        )
        calc = GDMBCalculatorRefactored(config)
        calc.initialize()

        # Verify components exist and are properly separated
        self.assertIsNotNone(calc._data_manager)
        self.assertIsNotNone(calc._computation)
        self.assertIsNotNone(calc._state_manager)
        self.assertIsNotNone(calc._figure_manager)

        # Verify data manager handles data operations
        self.assertTrue(hasattr(calc._data_manager, 'validate_y_data'))
        self.assertTrue(hasattr(calc._data_manager, 'buffer'))

        # Verify computation handles calculations
        self.assertTrue(hasattr(calc._computation, 'compute'))
        self.assertTrue(hasattr(calc._computation, 'calculate_scale_factor'))

        # Verify state manager handles state
        self.assertTrue(hasattr(calc._state_manager, 'get_flag'))
        self.assertTrue(hasattr(calc._state_manager, 'set_flag'))

        # Verify figure manager handles plotting
        self.assertTrue(hasattr(calc._figure_manager, 'create_figure'))
        self.assertTrue(hasattr(calc._figure_manager, 'add_axis'))

        calc.cleanup()


if __name__ == '__main__':
    unittest.main()
