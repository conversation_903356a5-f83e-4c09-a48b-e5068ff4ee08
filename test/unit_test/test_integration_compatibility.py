#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import unittest
import numpy as np
import tempfile
import os
from unittest.mock import Mock, patch
from pathlib import Path

from pxl.calc.de_calculator import DeltaECalculatorRefactored, DeltaECalculatorConfig, ScaleTarget
from pxl.calc.gdmb_calculator import GDMBCalculatorRefactored, GDMBCalculatorConfig
from pxl.calc.compatibility import DeltaECalculatorAdapter
from pxl.calc.base import CalculatorFactory
from pxl.de_calc.de_calc import DeltaECalculator
from pxl.gdmb_calc.gdmb_calc import GDMBCalculator
from pxl.util.logging_config import get_logger
from pxl.util.pusher import BytesIOPusher
from pxl.dlb.watermarks import WATERMARK_DLB_BASE64


class TestWebAPICompatibility(unittest.TestCase):
    """Test cases for Web API compatibility with refactored calculators."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
    
    def test_delta_e_web_api_interface(self):
        """Test that refactored Delta E calculator maintains Web API interface."""
        # Create calculator via adapter (as Web API would)
        calc = DeltaECalculatorAdapter(
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=DeltaECalculatorAdapter.EnumScaleTarget.AUTO,
            dynamic=False,
            show_figure=False
        )
        
        # Test Web API expected methods
        self.assertTrue(hasattr(calc, 'set_reference_white'))
        self.assertTrue(hasattr(calc, 'receive_data'))
        self.assertTrue(hasattr(calc, 'batch_receive_data'))
        self.assertTrue(hasattr(calc, 'get_report'))
        self.assertTrue(hasattr(calc, 'plot_figure'))
        self.assertTrue(hasattr(calc, 'cleanup'))
        
        # Test method signatures match expected interface
        target_xyz = np.array([0.3, 0.3, 0.3])
        measured_xyz = np.array([0.31, 0.29, 0.32])
        
        # These should not raise exceptions
        calc.set_reference_white(target_xyz, measured_xyz)
        calc.receive_data(0, target_xyz, measured_xyz)
        report = calc.get_report()
        calc.plot_figure(save_figure=None)
        
        # Verify report structure
        self.assertIsNotNone(report)
        self.assertTrue(hasattr(report, 'max_de'))
        self.assertTrue(hasattr(report, 'avg_de'))
        
        calc.cleanup()
    
    def test_gdmb_web_api_interface(self):
        """Test that refactored GDMB calculator maintains Web API interface."""
        # Create refactored calculator
        config = GDMBCalculatorConfig(
            tmax=500.0,
            debug=False,
            dynamic=False,
            show_figure=False
        )
        calc = GDMBCalculatorRefactored(config)
        calc.initialize()
        
        # Test Web API expected methods
        self.assertTrue(hasattr(calc, 'receive_data'))
        self.assertTrue(hasattr(calc, 'batch_receive_data'))
        self.assertTrue(hasattr(calc, 'get_report'))
        self.assertTrue(hasattr(calc, 'plot_figure'))
        self.assertTrue(hasattr(calc, 'scale_factor'))
        self.assertTrue(hasattr(calc, 'cleanup'))
        
        # Test method functionality
        max_flag = calc.receive_data(0, 100.0, 95.0)
        self.assertIsInstance(max_flag, bool)
        
        report = calc.get_report()
        self.assertIsInstance(report, dict)
        self.assertIn('tmax', report)
        self.assertIn('mmax', report)
        
        scale_factor = calc.scale_factor()
        self.assertIsInstance(scale_factor, float)
        
        calc.cleanup()
    
    def test_bytesio_pusher_integration(self):
        """Test BytesIO pusher integration for Web API."""
        # Create mock pusher
        mock_pusher = Mock(spec=BytesIOPusher)
        
        # Create calculator with pusher
        calc = DeltaECalculatorAdapter(
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=DeltaECalculatorAdapter.EnumScaleTarget.AUTO,
            dynamic=False,
            show_figure=False,
            bytesio_pusher=mock_pusher
        )
        
        # Process some data
        target_xyz = np.array([0.3, 0.3, 0.3])
        measured_xyz = np.array([0.31, 0.29, 0.32])
        
        calc.set_reference_white(target_xyz, measured_xyz)
        calc.receive_data(0, target_xyz, measured_xyz)
        
        # Plot figure (should trigger pusher)
        calc.plot_figure(save_figure=None)
        
        # Verify pusher was called (figure manager should push data)
        # Note: This depends on the internal implementation
        
        calc.cleanup()
    
    def test_factory_method_web_compatibility(self):
        """Test factory method compatibility with Web API."""
        # Test create_refactored factory method
        calc = DeltaECalculator.create_refactored(
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=DeltaECalculator.EnumScaleTarget.AUTO,
            dynamic=False,
            show_figure=False
        )
        
        # Should return adapter that maintains interface
        self.assertIsInstance(calc, DeltaECalculatorAdapter)
        
        # Test basic functionality
        target_xyz = np.array([0.3, 0.3, 0.3])
        measured_xyz = np.array([0.31, 0.29, 0.32])
        
        calc.set_reference_white(target_xyz, measured_xyz)
        calc.receive_data(0, target_xyz, measured_xyz)
        report = calc.get_report()
        
        self.assertIsNotNone(report)
        calc.cleanup()


class TestCalculatorFactory(unittest.TestCase):
    """Test cases for calculator factory functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
    
    def test_factory_registration(self):
        """Test calculator factory registration."""
        # Check that refactored calculators are registered
        available_calculators = CalculatorFactory.list_calculators()
        
        self.assertIn('delta_e_refactored', available_calculators)
        self.assertIn('gdmb_refactored', available_calculators)
    
    def test_factory_creation(self):
        """Test calculator creation via factory."""
        # Test Delta E calculator creation
        de_config = DeltaECalculatorConfig(
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=ScaleTarget.AUTO,
            dynamic=False,
            show_figure=False
        )
        
        de_calc = CalculatorFactory.create('delta_e_refactored', de_config)
        self.assertIsInstance(de_calc, DeltaECalculatorRefactored)
        de_calc.cleanup()
        
        # Test GDMB calculator creation
        gdmb_config = GDMBCalculatorConfig(
            tmax=500.0,
            debug=False,
            dynamic=False,
            show_figure=False
        )
        
        gdmb_calc = CalculatorFactory.create('gdmb_refactored', gdmb_config)
        self.assertIsInstance(gdmb_calc, GDMBCalculatorRefactored)
        gdmb_calc.cleanup()


class TestBackwardCompatibility(unittest.TestCase):
    """Test cases for backward compatibility."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
    
    def test_enum_compatibility(self):
        """Test enum compatibility between original and refactored."""
        # Test that adapter enums match original enums
        self.assertEqual(
            DeltaECalculatorAdapter.EnumScaleTarget.AUTO.value,
            DeltaECalculator.EnumScaleTarget.AUTO.value
        )
        self.assertEqual(
            DeltaECalculatorAdapter.EnumScaleTarget.YES.value,
            DeltaECalculator.EnumScaleTarget.YES.value
        )
        self.assertEqual(
            DeltaECalculatorAdapter.EnumScaleTarget.NO.value,
            DeltaECalculator.EnumScaleTarget.NO.value
        )
    
    def test_parameter_compatibility(self):
        """Test parameter compatibility."""
        # Test that adapter accepts same parameters as original
        common_params = {
            'max_de_threshold': (2.5, 10.0, float('inf')),
            'scale_target': DeltaECalculator.EnumScaleTarget.AUTO,
            'dynamic': False,
            'target_max': 0.0,
            'figure_size': (12.0, 7.0),
            'title_size': 8,
            'title_color': 'purple',
            'show_figure': False,
            'logo': WATERMARK_DLB_BASE64
        }
        
        # Should not raise exceptions
        original_calc = DeltaECalculator(**common_params)
        adapter_calc = DeltaECalculatorAdapter(**common_params)
        
        original_calc.cleanup()
        adapter_calc.cleanup()
    
    def test_method_signature_compatibility(self):
        """Test method signature compatibility."""
        calc = DeltaECalculatorAdapter(
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=DeltaECalculatorAdapter.EnumScaleTarget.AUTO,
            dynamic=False,
            show_figure=False
        )
        
        # Test method signatures match original
        target_xyz = np.array([0.3, 0.3, 0.3])
        measured_xyz = np.array([0.31, 0.29, 0.32])
        
        # These should work with same signatures as original
        calc.set_reference_white(target_xyz, measured_xyz)
        calc.receive_data(0, target_xyz, measured_xyz)
        calc.batch_receive_data([target_xyz], [measured_xyz])
        
        report = calc.get_report()
        self.assertIsNotNone(report)
        
        calc.plot_figure(save_figure=None)
        calc.cleanup()


class TestEndToEndIntegration(unittest.TestCase):
    """End-to-end integration tests."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
    
    def test_complete_delta_e_workflow(self):
        """Test complete Delta E calculation workflow."""
        # Create calculator
        calc = DeltaECalculatorAdapter(
            max_de_threshold=(2.5, 10.0, float('inf')),
            scale_target=DeltaECalculatorAdapter.EnumScaleTarget.AUTO,
            dynamic=False,
            show_figure=False
        )
        
        # Set reference white
        target_xyz = np.array([0.3127, 0.329, 0.3583])  # D65
        measured_xyz = np.array([0.31, 0.33, 0.36])
        calc.set_reference_white(target_xyz, measured_xyz)
        
        # Process multiple data points
        test_data = [
            (np.array([0.3, 0.3, 0.3]), np.array([0.31, 0.29, 0.32])),
            (np.array([0.5, 0.4, 0.2]), np.array([0.51, 0.39, 0.21])),
            (np.array([0.2, 0.6, 0.8]), np.array([0.19, 0.61, 0.79])),
        ]
        
        for i, (target, measured) in enumerate(test_data):
            calc.receive_data(i, target, measured)
        
        # Get report
        report = calc.get_report()
        self.assertGreater(report.max_de, 0)
        self.assertGreater(report.avg_de, 0)
        
        # Plot figure
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            tmp_filename = tmp_file.name
        
        try:
            calc.plot_figure(save_figure=tmp_filename)
            self.assertTrue(os.path.exists(tmp_filename))
            self.assertGreater(os.path.getsize(tmp_filename), 0)
        finally:
            if os.path.exists(tmp_filename):
                os.unlink(tmp_filename)
        
        calc.cleanup()
    
    def test_complete_gdmb_workflow(self):
        """Test complete GDMB calculation workflow."""
        # Create calculator
        config = GDMBCalculatorConfig(
            tmax=500.0,
            debug=False,
            dynamic=False,
            show_figure=False
        )
        calc = GDMBCalculatorRefactored(config)
        calc.initialize()
        
        # Process multiple data points
        test_data = [
            (100.0, 95.0),
            (200.0, 190.0),
            (300.0, 285.0),
            (400.0, 380.0),
            (500.0, 475.0),
        ]
        
        for i, (target, measured) in enumerate(test_data):
            max_flag = calc.receive_data(i, target, measured)
            self.assertIsInstance(max_flag, bool)
        
        # Get report and scale factor
        report = calc.get_report()
        self.assertEqual(report['tmax'], 500.0)
        self.assertGreaterEqual(report['mmax'], 0)
        
        scale_factor = calc.scale_factor()
        self.assertIsInstance(scale_factor, float)
        self.assertGreater(scale_factor, 0)
        
        # Plot figure
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            tmp_filename = tmp_file.name
        
        try:
            calc.plot_figure(save_figure=tmp_filename)
            self.assertTrue(os.path.exists(tmp_filename))
            self.assertGreater(os.path.getsize(tmp_filename), 0)
        finally:
            if os.path.exists(tmp_filename):
                os.unlink(tmp_filename)
        
        calc.cleanup()


if __name__ == '__main__':
    unittest.main()
