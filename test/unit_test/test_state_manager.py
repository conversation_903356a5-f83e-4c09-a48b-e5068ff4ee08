#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import unittest
import threading
import time
from unittest.mock import Mock, patch

from pxl.calc.state_manager import StateManager, TimerState
from pxl.calc.base import CalculatorState
from pxl.util.logging_config import get_logger


class TestStateManager(unittest.TestCase):
    """Test cases for StateManager class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
        self.state_manager = StateManager()
    
    def test_state_manager_initialization(self):
        """Test state manager initialization."""
        # Test initial state
        self.assertEqual(self.state_manager.state, CalculatorState.INITIALIZED)
        
        # Test default flags
        self.assertFalse(self.state_manager.get_flag('stop_draw'))
        self.assertFalse(self.state_manager.get_flag('receive_flag'))
        self.assertFalse(self.state_manager.get_flag('max_flag'))
        
        # Test default counters
        self.assertEqual(self.state_manager.get_counter('max_counter'), 0)
        self.assertEqual(self.state_manager.get_counter('pattern_index'), 0)
    
    def test_state_operations(self):
        """Test state operations."""
        # Test state changes
        self.state_manager.set_state(CalculatorState.RUNNING)
        self.assertEqual(self.state_manager.state, CalculatorState.RUNNING)

        self.state_manager.set_state(CalculatorState.PAUSED)
        self.assertEqual(self.state_manager.state, CalculatorState.PAUSED)

        self.state_manager.set_state(CalculatorState.ERROR)
        self.assertEqual(self.state_manager.state, CalculatorState.ERROR)
    
    def test_flag_operations(self):
        """Test flag operations."""
        # Test getting non-existent flag
        self.assertFalse(self.state_manager.get_flag('non_existent_flag'))
        
        # Test setting and getting flags
        self.state_manager.set_flag('test_flag', True)
        self.assertTrue(self.state_manager.get_flag('test_flag'))
        
        self.state_manager.set_flag('test_flag', False)
        self.assertFalse(self.state_manager.get_flag('test_flag'))
        
        # Test multiple flags
        self.state_manager.set_flag('flag1', True)
        self.state_manager.set_flag('flag2', False)
        self.state_manager.set_flag('flag3', True)
        
        self.assertTrue(self.state_manager.get_flag('flag1'))
        self.assertFalse(self.state_manager.get_flag('flag2'))
        self.assertTrue(self.state_manager.get_flag('flag3'))
    
    def test_counter_operations(self):
        """Test counter operations."""
        # Test getting non-existent counter
        self.assertEqual(self.state_manager.get_counter('non_existent_counter'), 0)
        
        # Test setting and getting counters
        self.state_manager.set_counter('test_counter', 5)
        self.assertEqual(self.state_manager.get_counter('test_counter'), 5)
        
        # Test incrementing counters
        self.state_manager.increment_counter('test_counter', 3)
        self.assertEqual(self.state_manager.get_counter('test_counter'), 8)
        
        # Test incrementing non-existent counter
        self.state_manager.increment_counter('new_counter', 10)
        self.assertEqual(self.state_manager.get_counter('new_counter'), 10)
        
        # Test decrementing counters
        self.state_manager.increment_counter('test_counter', -2)
        self.assertEqual(self.state_manager.get_counter('test_counter'), 6)
    
    def test_timer_operations(self):
        """Test timer operations."""
        timer_name = 'test_timer'
        callback = Mock()

        # Test creating timer
        self.state_manager.create_timer(timer_name, 1.0, callback)
        self.assertEqual(self.state_manager.get_timer_state(timer_name), TimerState.CREATED)

        # Test starting timer
        self.state_manager.start_timer(timer_name)
        self.assertEqual(self.state_manager.get_timer_state(timer_name), TimerState.RUNNING)

        # Test pausing timer
        self.state_manager.pause_timer(timer_name)
        self.assertEqual(self.state_manager.get_timer_state(timer_name), TimerState.PAUSED)

        # Test resuming timer
        self.state_manager.resume_timer(timer_name)
        self.assertEqual(self.state_manager.get_timer_state(timer_name), TimerState.RUNNING)

        # Test stopping timer
        self.state_manager.stop_timer(timer_name)
        self.assertEqual(self.state_manager.get_timer_state(timer_name), TimerState.STOPPED)
    
    def test_timer_elapsed_time(self):
        """Test timer elapsed time calculation."""
        timer_name = 'elapsed_test_timer'
        callback = Mock()

        # Create and start timer
        self.state_manager.create_timer(timer_name, 1.0, callback)
        self.state_manager.start_timer(timer_name)
        
        # Wait a bit
        time.sleep(0.1)
        
        # Check elapsed time
        elapsed = self.state_manager.get_timer_elapsed(timer_name)
        self.assertGreaterEqual(elapsed, 0.1)
        self.assertLess(elapsed, 0.2)  # Should be close to 0.1
        
        # Stop timer and check elapsed time doesn't change
        self.state_manager.stop_timer(timer_name)
        elapsed_stopped = self.state_manager.get_timer_elapsed(timer_name)
        
        time.sleep(0.1)
        elapsed_after_wait = self.state_manager.get_timer_elapsed(timer_name)
        self.assertEqual(elapsed_stopped, elapsed_after_wait)
    
    def test_timer_pause_resume(self):
        """Test timer pause and resume functionality."""
        timer_name = 'pause_resume_timer'
        callback = Mock()

        # Create and start timer
        self.state_manager.create_timer(timer_name, 1.0, callback)
        self.state_manager.start_timer(timer_name)
        
        # Wait and pause
        time.sleep(0.1)
        self.state_manager.pause_timer(timer_name)
        elapsed_at_pause = self.state_manager.get_timer_elapsed(timer_name)
        
        # Wait while paused
        time.sleep(0.1)
        elapsed_while_paused = self.state_manager.get_timer_elapsed(timer_name)
        self.assertEqual(elapsed_at_pause, elapsed_while_paused)
        
        # Resume and wait
        self.state_manager.resume_timer(timer_name)
        time.sleep(0.1)
        elapsed_after_resume = self.state_manager.get_timer_elapsed(timer_name)
        self.assertGreater(elapsed_after_resume, elapsed_at_pause)
    
    def test_reset_functionality(self):
        """Test reset functionality."""
        # Set some state
        self.state_manager.set_state(CalculatorState.RUNNING)
        self.state_manager.set_flag('test_flag', True)
        self.state_manager.set_counter('test_counter', 10)
        callback = Mock()
        self.state_manager.create_timer('test_timer', 1.0, callback)
        
        # Verify state before reset
        self.assertEqual(self.state_manager.state, CalculatorState.RUNNING)
        self.assertTrue(self.state_manager.get_flag('test_flag'))
        self.assertEqual(self.state_manager.get_counter('test_counter'), 10)
        self.assertIsNotNone(self.state_manager.get_timer_state('test_timer'))
        
        # Reset
        self.state_manager.reset()
        
        # Verify state after reset
        self.assertEqual(self.state_manager.state, CalculatorState.INITIALIZED)
        self.assertFalse(self.state_manager.get_flag('test_flag'))
        self.assertEqual(self.state_manager.get_counter('test_counter'), 0)
        self.assertIsNone(self.state_manager.get_timer_state('test_timer'))
        
        # Verify default flags and counters are restored
        self.assertFalse(self.state_manager.get_flag('stop_draw'))
        self.assertEqual(self.state_manager.get_counter('max_counter'), 0)
    
    def test_thread_safety(self):
        """Test thread safety of state manager."""
        results = []
        
        def worker(worker_id):
            for i in range(100):
                # Set flags and counters
                self.state_manager.set_flag(f'flag_{worker_id}_{i}', True)
                self.state_manager.increment_counter(f'counter_{worker_id}', 1)
                
                # Read flags and counters
                flag_value = self.state_manager.get_flag(f'flag_{worker_id}_{i}')
                counter_value = self.state_manager.get_counter(f'counter_{worker_id}')
                
                results.append((worker_id, i, flag_value, counter_value))
        
        # Start multiple threads
        threads = []
        for worker_id in range(5):
            thread = threading.Thread(target=worker, args=(worker_id,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify results
        self.assertEqual(len(results), 500)  # 5 workers * 100 operations each
        
        # Verify final counter values
        for worker_id in range(5):
            final_count = self.state_manager.get_counter(f'counter_{worker_id}')
            self.assertEqual(final_count, 100)
    
    def test_state_summary(self):
        """Test state summary functionality."""
        # Set some state
        self.state_manager.set_state(CalculatorState.RUNNING)
        self.state_manager.set_flag('test_flag', True)
        self.state_manager.set_counter('test_counter', 42)
        callback = Mock()
        self.state_manager.create_timer('test_timer', 1.0, callback)
        
        # Get state summary
        summary = self.state_manager.get_state_summary()
        
        # Verify summary contains expected information
        self.assertIn('calculator_state', summary)
        self.assertIn('flags', summary)
        self.assertIn('counters', summary)
        self.assertIn('timers', summary)

        self.assertEqual(summary['calculator_state'], CalculatorState.RUNNING.value)
        self.assertIn('test_flag', summary['flags'])
        self.assertTrue(summary['flags']['test_flag'])
        self.assertIn('test_counter', summary['counters'])
        self.assertEqual(summary['counters']['test_counter'], 42)
        self.assertIn('test_timer', summary['timers'])
    
    def test_cleanup(self):
        """Test cleanup functionality."""
        # Create some timers
        callback1 = Mock()
        callback2 = Mock()
        self.state_manager.create_timer('timer1', 1.0, callback1)
        self.state_manager.create_timer('timer2', 2.0, callback2)
        self.state_manager.start_timer('timer1')
        
        # Cleanup
        self.state_manager.cleanup()
        
        # Verify timers are cleaned up
        self.assertIsNone(self.state_manager.get_timer_state('timer1'))
        self.assertIsNone(self.state_manager.get_timer_state('timer2'))


if __name__ == '__main__':
    unittest.main()
