#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    :author: <PERSON>
    :contact: <EMAIL>
    :copyright: Copyright 2024 Dolby Laboratories inc.
    :license: Proprietary.
    :created: Dec 19, 2024
"""

import unittest
import numpy as np
from unittest.mock import Mock, patch

from pxl.calc.computation import DeltaEComputation, GDMBComputation, BaseComputation
from pxl.util.exceptions import CalculationError
from pxl.util.logging_config import get_logger


class TestBaseComputation(unittest.TestCase):
    """Test cases for BaseComputation class."""

    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
        # BaseComputation is abstract, so we'll test it through DeltaEComputation
        self.computation = DeltaEComputation()

    def test_base_computation_initialization(self):
        """Test base computation initialization."""
        self.assertEqual(self.computation.computation_count, 0)
        self.assertIsNotNone(self.computation._logger)

    def test_abstract_methods_exist(self):
        """Test that abstract methods are implemented."""
        # Test that the concrete class has the required methods
        self.assertTrue(hasattr(self.computation, 'compute'))
        self.assertTrue(hasattr(self.computation, 'validate_inputs'))
        self.assertTrue(callable(getattr(self.computation, 'compute')))
        self.assertTrue(callable(getattr(self.computation, 'validate_inputs')))


class TestDeltaEComputation(unittest.TestCase):
    """Test cases for DeltaEComputation class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
        self.computation = DeltaEComputation()
        self.ref_white = np.array([0.3127, 0.329, 0.3583])  # D65
    
    def test_delta_e_computation_initialization(self):
        """Test Delta E computation initialization."""
        self.assertEqual(self.computation.computation_count, 0)
        self.assertEqual(self.computation.max_delta_e, 0.0)
        self.assertEqual(self.computation.average_delta_e, 0.0)
        self.assertIsNone(self.computation.reference_white)
    
    def test_reference_white_operations(self):
        """Test reference white operations."""
        # Set reference white
        self.computation.set_reference_white(self.ref_white)
        np.testing.assert_array_equal(self.computation.reference_white, self.ref_white)
        
        # Test with different reference white
        new_ref_white = np.array([0.31, 0.32, 0.33])
        self.computation.set_reference_white(new_ref_white)
        np.testing.assert_array_equal(self.computation.reference_white, new_ref_white)
    
    def test_input_validation(self):
        """Test input validation."""
        self.computation.set_reference_white(self.ref_white)
        
        # Valid inputs
        target_xyz = np.array([0.3, 0.3, 0.3])
        measured_xyz = np.array([0.31, 0.29, 0.32])
        self.computation.validate_inputs(target_xyz, measured_xyz)  # Should not raise
        
        # Invalid inputs - wrong shape
        with self.assertRaises(CalculationError):
            self.computation.validate_inputs(np.array([0.3, 0.3]), measured_xyz)
        
        # Invalid inputs - contains NaN
        with self.assertRaises(CalculationError):
            self.computation.validate_inputs(np.array([0.3, np.nan, 0.3]), measured_xyz)
        
        # Invalid inputs - no reference white
        computation_no_ref = DeltaEComputation()
        with self.assertRaises(CalculationError):
            computation_no_ref.validate_inputs(target_xyz, measured_xyz)
    
    def test_delta_e_computation(self):
        """Test Delta E computation."""
        self.computation.set_reference_white(self.ref_white)
        
        # Test computation with identical colors (should be near zero)
        target_xyz = np.array([0.3, 0.3, 0.3])
        measured_xyz = np.array([0.3, 0.3, 0.3])
        
        delta_e = self.computation.compute(target_xyz, measured_xyz)
        self.assertIsInstance(delta_e, float)
        self.assertAlmostEqual(delta_e, 0.0, places=2)
        
        # Test computation with different colors
        measured_xyz_diff = np.array([0.31, 0.29, 0.32])
        delta_e_diff = self.computation.compute(target_xyz, measured_xyz_diff)
        self.assertGreater(delta_e_diff, 0)
        
        # Verify statistics are updated
        self.assertEqual(self.computation.computation_count, 2)
        self.assertEqual(self.computation.max_delta_e, max(delta_e, delta_e_diff))
        self.assertAlmostEqual(self.computation.average_delta_e, (delta_e + delta_e_diff) / 2, places=4)
    
    def test_scale_multiplier_effect(self):
        """Test effect of scale multiplier on computation."""
        self.computation.set_reference_white(self.ref_white)

        target_xyz = np.array([0.3, 0.3, 0.3])
        measured_xyz = np.array([0.31, 0.29, 0.32])

        # Compute with scale multiplier 1.0
        delta_e_1 = self.computation.compute(target_xyz, measured_xyz, scale_multiplier=1.0)

        # Create new computation instance and compute with scale multiplier 2.0
        computation_2 = DeltaEComputation()
        computation_2.set_reference_white(self.ref_white)
        delta_e_2 = computation_2.compute(target_xyz, measured_xyz, scale_multiplier=2.0)

        # Results should be different
        self.assertNotAlmostEqual(delta_e_1, delta_e_2, places=2)
    
    def test_computation_statistics(self):
        """Test computation statistics tracking."""
        self.computation.set_reference_white(self.ref_white)
        
        target_xyz = np.array([0.3, 0.3, 0.3])
        delta_e_values = []
        
        # Perform multiple computations
        for i in range(5):
            measured_xyz = np.array([0.3 + i * 0.01, 0.3 - i * 0.005, 0.3 + i * 0.008])
            delta_e = self.computation.compute(target_xyz, measured_xyz)
            delta_e_values.append(delta_e)
        
        # Verify statistics
        self.assertEqual(self.computation.computation_count, 5)
        self.assertEqual(self.computation.max_delta_e, max(delta_e_values))
        expected_avg = sum(delta_e_values) / len(delta_e_values)
        self.assertAlmostEqual(self.computation.average_delta_e, expected_avg, places=4)
    
    def test_computation_state_tracking(self):
        """Test computation state tracking."""
        self.computation.set_reference_white(self.ref_white)

        # Perform some computations
        target_xyz = np.array([0.3, 0.3, 0.3])
        measured_xyz = np.array([0.31, 0.29, 0.32])
        self.computation.compute(target_xyz, measured_xyz)

        # Verify state tracking
        self.assertGreater(self.computation.computation_count, 0)
        self.assertGreater(self.computation.max_delta_e, 0)
        self.assertGreater(self.computation.average_delta_e, 0)

        # Perform another computation
        measured_xyz2 = np.array([0.32, 0.28, 0.33])
        self.computation.compute(target_xyz, measured_xyz2)

        # Verify updated state
        self.assertEqual(self.computation.computation_count, 2)
        self.assertGreater(self.computation.max_delta_e, 0)
        self.assertGreater(self.computation.average_delta_e, 0)


class TestGDMBComputation(unittest.TestCase):
    """Test cases for GDMBComputation class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = get_logger(__name__)
        self.tmax = 500.0
        self.computation = GDMBComputation(tmax=self.tmax)
    
    def test_gdmb_computation_initialization(self):
        """Test GDMB computation initialization."""
        self.assertEqual(self.computation.tmax, self.tmax)
        self.assertEqual(self.computation.mmax, 0.0)
        self.assertEqual(self.computation.scale_ratio, 0.0)
        self.assertEqual(len(self.computation.target_data), 0)
        self.assertEqual(len(self.computation.measurement_data), 0)
        self.assertEqual(len(self.computation.scaled_data), 0)
    
    def test_input_validation(self):
        """Test input validation."""
        # Valid inputs
        self.computation.validate_inputs(100.0, 95.0)  # Should not raise
        
        # Invalid inputs - negative values
        with self.assertRaises(CalculationError):
            self.computation.validate_inputs(-1.0, 95.0)
        
        with self.assertRaises(CalculationError):
            self.computation.validate_inputs(100.0, -1.0)
        
        # Invalid inputs - NaN
        with self.assertRaises(CalculationError):
            self.computation.validate_inputs(np.nan, 95.0)
        
        # Invalid inputs - not numbers
        with self.assertRaises(CalculationError):
            self.computation.validate_inputs("not_a_number", 95.0)
    
    def test_gdmb_computation(self):
        """Test GDMB computation."""
        target_y = 100.0
        measured_y = 95.0
        
        # Perform computation
        max_flag = self.computation.compute(target_y, measured_y)
        self.assertIsInstance(max_flag, bool)
        
        # Verify data storage
        self.assertEqual(len(self.computation.target_data), 1)
        self.assertEqual(len(self.computation.measurement_data), 1)
        self.assertEqual(self.computation.target_data[0], target_y)
        self.assertEqual(self.computation.measurement_data[0], measured_y)
        
        # Verify mmax update
        self.assertEqual(self.computation.mmax, measured_y)
    
    def test_max_flag_detection(self):
        """Test max flag detection."""
        # Test with values below tmax
        max_flag_1 = self.computation.compute(100.0, 95.0)
        self.assertFalse(max_flag_1)
        
        # Test with values at tmax
        max_flag_2 = self.computation.compute(self.tmax, self.tmax * 0.95)
        # Max flag behavior depends on internal logic
        self.assertIsInstance(max_flag_2, bool)
    
    def test_scale_factor_calculation(self):
        """Test scale factor calculation."""
        # Add some data points
        self.computation.compute(100.0, 95.0)
        self.computation.compute(200.0, 190.0)
        self.computation.compute(300.0, 285.0)
        
        # Calculate scale factor
        scale_factor = self.computation.calculate_scale_factor()
        self.assertIsInstance(scale_factor, float)
        self.assertGreater(scale_factor, 0)
    
    def test_batch_data_processing(self):
        """Test batch data processing."""
        target_data = [100.0, 200.0, 300.0, 400.0]
        measurement_data = [95.0, 190.0, 285.0, 380.0]
        
        # Process batch data
        for target, measured in zip(target_data, measurement_data):
            self.computation.compute(target, measured)
        
        # Verify all data was stored
        self.assertEqual(len(self.computation.target_data), 4)
        self.assertEqual(len(self.computation.measurement_data), 4)
        self.assertEqual(self.computation.target_data, target_data)
        self.assertEqual(self.computation.measurement_data, measurement_data)
        
        # Verify mmax
        self.assertEqual(self.computation.mmax, max(measurement_data))
    
    def test_data_accumulation(self):
        """Test data accumulation functionality."""
        # Add some data
        self.computation.compute(100.0, 95.0)
        self.computation.compute(200.0, 190.0)

        # Verify data accumulation
        self.assertEqual(len(self.computation.target_data), 2)
        self.assertEqual(len(self.computation.measurement_data), 2)
        self.assertGreater(self.computation.mmax, 0)

        # Verify data values
        self.assertEqual(self.computation.target_data[0], 100.0)
        self.assertEqual(self.computation.target_data[1], 200.0)
        self.assertEqual(self.computation.measurement_data[0], 95.0)
        self.assertEqual(self.computation.measurement_data[1], 190.0)

        # Verify mmax is the maximum of measured values
        self.assertEqual(self.computation.mmax, max(95.0, 190.0))


if __name__ == '__main__':
    unittest.main()
